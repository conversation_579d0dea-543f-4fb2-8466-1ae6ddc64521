"use server"

// Authentication actions for Trustay API
import { serverApiClient, ApiError } from '../lib/server-api-client';
import { ServerTokenManager } from '../lib/server-token-manager';
import {
  LoginRequest,
  RegisterRequest,
  RegisterDirectRequest,
  ChangePasswordRequest,
  AuthResponse,
  VerificationResponse,
  PasswordStrengthResponse,
  GeneratePasswordResponse,
  UserProfile,
} from '../types/types';

// Send email verification code
export const sendEmailVerification = async (
  email: string
): Promise<VerificationResponse> => {
  try {
    const response = await serverApiClient.post<VerificationResponse>('/api/verification/send', {
      type: 'email',
      email,
    });

    if (response.success && response.data) {
      return response.data;
    }

    throw new ApiError('Failed to send verification email', 400);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error while sending verification email', 0);
  }
};

// Verify email code
export const verifyEmailCode = async (
  email: string,
  code: string
): Promise<VerificationResponse> => {
  try {
    const response = await serverApiClient.post<VerificationResponse>('/api/verification/verify', {
      type: 'email',
      email,
      code,
    });

    if (response.success && response.data) {
      return response.data;
    }

    throw new ApiError('Failed to verify email code', 400);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error while verifying email code', 0);
  }
};

// Register with verification
export const registerWithVerification = async (
  userData: RegisterRequest,
  verificationToken: string
): Promise<AuthResponse> => {
  try {
    const response = await serverApiClient.post<AuthResponse>('/api/auth/register', userData, {
      headers: {
        'X-Verification-Token': verificationToken,
      },
    });

    if (response.success && response.data) {
      // Store tokens
      ServerTokenManager.setAccessToken(response.data.access_token);
      ServerTokenManager.setRefreshToken(response.data.refresh_token);

      return response.data;
    }

    throw new ApiError('Registration failed', 400);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error during registration', 0);
  }
};

// Register direct (for development)
export const registerDirect = async (
  userData: RegisterDirectRequest
): Promise<AuthResponse> => {
  try {
    const response = await serverApiClient.post<AuthResponse>('/api/auth/register-direct', userData);

    if (response.success && response.data) {
      // Store tokens
      ServerTokenManager.setAccessToken(response.data.access_token);
      ServerTokenManager.setRefreshToken(response.data.refresh_token);

      return response.data;
    }

    throw new ApiError('Direct registration failed', 400);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error during direct registration', 0);
  }
};

// Login
export const login = async (credentials: LoginRequest): Promise<AuthResponse> => {
  try {
    const response = await serverApiClient.post<AuthResponse>('/api/auth/login', credentials);

    if (response.success && response.data) {
      // Store tokens
      ServerTokenManager.setAccessToken(response.data.access_token);
      ServerTokenManager.setRefreshToken(response.data.refresh_token);

      return response.data;
    }

    throw new ApiError('Login failed', 401);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error during login', 0);
  }
};

// Get current user
export const getCurrentUser = async (): Promise<UserProfile> => {
  try {
    const response = await serverApiClient.get<UserProfile>('/api/auth/me');

    if (response.success && response.data) {
      return response.data;
    }

    throw new ApiError('Failed to get current user', 401);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error while getting current user', 0);
  }
};

// Refresh token
export const refreshToken = async (): Promise<AuthResponse> => {
  const refreshTokenValue = ServerTokenManager.getRefreshToken();

  if (!refreshTokenValue) {
    throw new ApiError('No refresh token available', 401);
  }

  try {
    const response = await serverApiClient.post<AuthResponse>('/api/auth/refresh', {
      refreshToken: refreshTokenValue,
    });

    if (response.success && response.data) {
      // Update stored tokens
      ServerTokenManager.setAccessToken(response.data.access_token);
      ServerTokenManager.setRefreshToken(response.data.refresh_token);

      return response.data;
    }

    throw new ApiError('Token refresh failed', 401);
  } catch (error) {
    // Clear tokens if refresh fails
    ServerTokenManager.clearTokens();

    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error during token refresh', 0);
  }
};

// Change password
export const changePassword = async (
  passwordData: ChangePasswordRequest
): Promise<{ message: string }> => {
  try {
    const response = await serverApiClient.put<{ message: string }>('/api/auth/change-password', passwordData);

    if (response.success && response.data) {
      return response.data;
    }

    throw new ApiError('Failed to change password', 400);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error while changing password', 0);
  }
};

// Check password strength
export const checkPasswordStrength = async (
  password: string
): Promise<PasswordStrengthResponse> => {
  try {
    const response = await serverApiClient.post<PasswordStrengthResponse>('/api/auth/check-password-strength', {
      password,
    });

    if (response.success && response.data) {
      return response.data;
    }

    throw new ApiError('Failed to check password strength', 400);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error while checking password strength', 0);
  }
};

// Generate secure password
export const generateSecurePassword = async (
  length: number = 16
): Promise<GeneratePasswordResponse> => {
  try {
    const response = await serverApiClient.get<GeneratePasswordResponse>(`/api/auth/generate-password?length=${length}`);

    if (response.success && response.data) {
      return response.data;
    }

    throw new ApiError('Failed to generate password', 400);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error while generating password', 0);
  }
};

// Revoke refresh token
export const revokeRefreshToken = async (token?: string): Promise<{ message: string }> => {
  const tokenToRevoke = token || ServerTokenManager.getRefreshToken();

  if (!tokenToRevoke) {
    throw new ApiError('No refresh token to revoke', 400);
  }

  try {
    const response = await serverApiClient.post<{ message: string }>('/api/auth/revoke', {
      refreshToken: tokenToRevoke,
    });

    if (response.success && response.data) {
      return response.data;
    }

    throw new ApiError('Failed to revoke refresh token', 400);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error while revoking refresh token', 0);
  }
};

// Revoke all refresh tokens
export const revokeAllRefreshTokens = async (): Promise<{ message: string }> => {
  try {
    const response = await serverApiClient.post<{ message: string }>('/api/auth/revoke-all');

    if (response.success && response.data) {
      return response.data;
    }

    throw new ApiError('Failed to revoke all refresh tokens', 400);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error while revoking all refresh tokens', 0);
  }
};

// Logout
export const logout = async (): Promise<void> => {
  try {
    // Try to revoke refresh token before clearing
    const refreshTokenValue = ServerTokenManager.getRefreshToken();
    if (refreshTokenValue) {
      await revokeRefreshToken(refreshTokenValue);
    }
  } catch (error) {
    // Continue with logout even if revoke fails
    console.warn('Failed to revoke refresh token during logout:', error);
  } finally {
    // Always clear tokens
    ServerTokenManager.clearTokens();
  }
};

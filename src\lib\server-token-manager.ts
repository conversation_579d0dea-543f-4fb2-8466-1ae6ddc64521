import { cookies } from 'next/headers'

// Server-side token management using cookies
export const ServerTokenManager = {
  getAccessToken: (): string | null => {
    try {
      const cookieStore = cookies()
      return cookieStore.get('accessToken')?.value || null
    } catch {
      return null
    }
  },

  setAccessToken: (token: string): void => {
    try {
      const cookieStore = cookies()
      cookieStore.set('accessToken', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 60 * 60 * 24 * 7, // 7 days
      })
    } catch (error) {
      console.error('Failed to set access token:', error)
    }
  },

  getRefreshToken: (): string | null => {
    try {
      const cookieStore = cookies()
      return cookieStore.get('refreshToken')?.value || null
    } catch {
      return null
    }
  },

  setRefreshToken: (token: string): void => {
    try {
      const cookieStore = cookies()
      cookieStore.set('refreshToken', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 60 * 60 * 24 * 30, // 30 days
      })
    } catch (error) {
      console.error('Failed to set refresh token:', error)
    }
  },

  clearTokens: (): void => {
    try {
      const cookieStore = cookies()
      cookieStore.delete('accessToken')
      cookieStore.delete('refreshToken')
    } catch (error) {
      console.error('Failed to clear tokens:', error)
    }
  },
}

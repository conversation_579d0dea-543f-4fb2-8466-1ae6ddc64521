"use client"

// Client-side token management that reads from cookies
export const ClientTokenManager = {
  getAccessToken: (): string | null => {
    if (typeof document === 'undefined') return null;
    
    // Read from cookies
    const cookies = document.cookie.split(';');
    const accessTokenCookie = cookies.find(cookie => 
      cookie.trim().startsWith('accessToken=')
    );
    
    if (accessTokenCookie) {
      return accessTokenCookie.split('=')[1];
    }
    
    // Fallback to localStorage for backward compatibility
    return localStorage.getItem('accessToken');
  },

  getRefreshToken: (): string | null => {
    if (typeof document === 'undefined') return null;
    
    // Read from cookies
    const cookies = document.cookie.split(';');
    const refreshTokenCookie = cookies.find(cookie => 
      cookie.trim().startsWith('refreshToken=')
    );
    
    if (refreshTokenCookie) {
      return refreshTokenCookie.split('=')[1];
    }
    
    // Fallback to localStorage for backward compatibility
    return localStorage.getItem('refreshToken');
  },

  // For backward compatibility, still support localStorage
  setAccessToken: (token: string): void => {
    if (typeof window === 'undefined') return;
    localStorage.setItem('accessToken', token);
  },

  setRefreshToken: (token: string): void => {
    if (typeof window === 'undefined') return;
    localStorage.setItem('refreshToken', token);
  },

  clearTokens: (): void => {
    if (typeof window === 'undefined') return;
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    
    // Also clear cookies by setting them to expire
    document.cookie = 'accessToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    document.cookie = 'refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
  },
};

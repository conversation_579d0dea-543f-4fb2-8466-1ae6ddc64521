// Health check utility to test API connection
import { apiClient } from '../lib/api-client';

export const healthCheck = async (): Promise<boolean> => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/`);
    return response.ok;
  } catch (error) {
    console.error('Health check failed:', error);
    return false;
  }
};

export const apiHealthCheck = async (): Promise<boolean> => {
  try {
    const response = await apiClient.get('/');
    return response.success;
  } catch (error) {
    console.error('API health check failed:', error);
    return false;
  }
};

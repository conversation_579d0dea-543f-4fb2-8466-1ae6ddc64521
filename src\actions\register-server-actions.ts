"use server"

import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

// Server action for handling registration completion
export async function completeRegistration(formData: FormData) {
  const role = formData.get('role') as string
  
  // After successful registration, redirect based on role
  if (role === 'tenant') {
    redirect('/dashboard/tenant')
  } else if (role === 'landlord') {
    redirect('/dashboard/landlord')
  } else {
    redirect('/')
  }
}

// Server action for skipping profile update
export async function skipProfileUpdate() {
  redirect('/')
}

// Server action for setting auth cookies (if needed in the future)
export async function setAuthCookies(accessToken: string, refreshToken: string) {
  const cookieStore = cookies()
  
  // Set secure, httpOnly cookies for tokens
  cookieStore.set('accessToken', accessToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 60 * 60 * 24 * 7, // 7 days
  })
  
  cookieStore.set('refreshToken', refreshToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 60 * 60 * 24 * 30, // 30 days
  })
}

// Server action for clearing auth cookies
export async function clearAuthCookies() {
  const cookieStore = cookies()
  cookieStore.delete('accessToken')
  cookieStore.delete('refreshToken')
}

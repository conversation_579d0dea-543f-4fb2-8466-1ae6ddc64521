"use client"

// Client-side wrappers for server actions
import { 
  sendEmail<PERSON>erification as serverSend<PERSON>mailVerification,
  verify<PERSON>mail<PERSON><PERSON> as serverVerifyEmailCode,
  registerWithVerification as serverRegisterWithVerification,
  registerDirect as serverRegisterDirect,
  updateUserProfile as serverUpdate<PERSON>serProfile,
  login as server<PERSON><PERSON>in,
  get<PERSON><PERSON><PERSON><PERSON><PERSON> as serverGetCurrentUser,
  logout as serverLogout
} from './auth-actions'

import { ClientTokenManager } from '@/lib/client-token-manager'

// Client-side wrappers that handle token synchronization
export const sendEmailVerification = async (email: string) => {
  return await serverSendEmailVerification(email)
}

export const verifyEmailCode = async (email: string, code: string) => {
  return await serverVerifyEmailCode(email, code)
}

export const registerWithVerification = async (userData: any, verificationToken: string) => {
  const result = await serverRegisterWithVerification(userData, verificationToken)
  
  // Sync tokens to localStorage for backward compatibility
  if (result.access_token) {
    ClientTokenManager.setAccessToken(result.access_token)
  }
  if (result.refresh_token) {
    ClientTokenManager.setRefreshToken(result.refresh_token)
  }
  
  return result
}

export const registerDirect = async (userData: any) => {
  const result = await serverRegisterDirect(userData)
  
  // Sync tokens to localStorage for backward compatibility
  if (result.access_token) {
    ClientTokenManager.setAccessToken(result.access_token)
  }
  if (result.refresh_token) {
    ClientTokenManager.setRefreshToken(result.refresh_token)
  }
  
  return result
}

export const updateUserProfile = async (profileData: any) => {
  return await serverUpdateUserProfile(profileData)
}

export const login = async (credentials: any) => {
  const result = await serverLogin(credentials)
  
  // Sync tokens to localStorage for backward compatibility
  if (result.access_token) {
    ClientTokenManager.setAccessToken(result.access_token)
  }
  if (result.refresh_token) {
    ClientTokenManager.setRefreshToken(result.refresh_token)
  }
  
  return result
}

export const getCurrentUser = async () => {
  return await serverGetCurrentUser()
}

export const logout = async () => {
  await serverLogout()
  ClientTokenManager.clearTokens()
}

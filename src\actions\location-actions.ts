// Location services actions for Trustay API
import { apiClient, ApiError } from '../lib/api-client';
import { Province, District, Ward } from '../types/types';

// Get all provinces
export const getProvinces = async (): Promise<Province[]> => {
  try {
    const response = await apiClient.get<Province[]>('/api/provinces');
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new ApiError('Failed to get provinces', 400);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error while getting provinces', 0);
  }
};

// Get districts by province
export const getDistrictsByProvince = async (provinceId: string): Promise<District[]> => {
  try {
    const response = await apiClient.get<District[]>('/api/districts', { provinceId });
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new ApiError('Failed to get districts', 400);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error while getting districts', 0);
  }
};

// Get wards by district
export const getWardsByDistrict = async (districtId: string): Promise<Ward[]> => {
  try {
    const response = await apiClient.get<Ward[]>('/api/wards', { districtId });
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new ApiError('Failed to get wards', 400);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error while getting wards', 0);
  }
};
